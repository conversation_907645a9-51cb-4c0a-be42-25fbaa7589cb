# Import Required Libraries
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import seaborn as sns
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Configure plotting
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

# Database connection
import psycopg2
from sqlalchemy import create_engine
import json

print("✅ Libraries imported successfully!")
print(f"📊 Pandas version: {pd.__version__}")
print(f"🎨 Plotly version: {px.__version__}")
print(f"🔗 SQLAlchemy ready for database connection")

# Database Connection and Data Loading
DATABASE_URI = "*****************************************************************"

# Create database engine
engine = create_engine(DATABASE_URI)

# Load alarm data
query = """
SELECT 
    id,
    alarm_no,
    alarm_type,
    alarm_type_name,
    alarm_level,
    alarm_way,
    address,
    dept_name,
    alarm_first_time,
    alarm_last_time,
    status,
    duration,
    alarm_frequency,
    alarm_area_name,
    resource_name,
    deal_result_view,
    deal_time,
    deal_user_name,
    consuming
FROM alarm.alm_alarm_history 
WHERE alarm_no > '20250620'
ORDER BY alarm_first_time DESC
"""

print("🔄 Loading alarm data from database...")
df = pd.read_sql_query(query, engine)

print(f"✅ Data loaded successfully!")
print(f"📈 Total records: {len(df):,}")
print(f"📅 Date range: {df['alarm_first_time'].min()} to {df['alarm_first_time'].max()}")
print(f"🏭 Unique locations: {df['alarm_area_name'].nunique()}")
print(f"⚠️  Alarm types: {df['alarm_type_name'].nunique()}")

# Display basic info
df.info()

# Data Preprocessing and Feature Engineering

# Convert datetime columns
df['alarm_first_time'] = pd.to_datetime(df['alarm_first_time'])
df['alarm_last_time'] = pd.to_datetime(df['alarm_last_time'])
df['deal_time'] = pd.to_datetime(df['deal_time'])

# Create time-based features
df['alarm_date'] = df['alarm_first_time'].dt.date
df['alarm_hour'] = df['alarm_first_time'].dt.hour
df['alarm_day_of_week'] = df['alarm_first_time'].dt.day_name()
df['alarm_month'] = df['alarm_first_time'].dt.month
df['alarm_year'] = df['alarm_first_time'].dt.year

# Calculate response time (if deal_time exists)
df['response_time_hours'] = (df['deal_time'] - df['alarm_first_time']).dt.total_seconds() / 3600

# Create severity categories based on alarm level
def categorize_severity(level):
    if pd.isna(level):
        return 'Unknown'
    elif level in ['01', '02', '03']:
        return 'Critical'
    elif level in ['04', '05', '06']:
        return 'High'
    elif level in ['07', '08', '09']:
        return 'Medium'
    else:
        return 'Low'

df['severity_category'] = df['alarm_level'].apply(categorize_severity)

# Create alarm duration categories
df['alarm_duration_hours'] = (df['alarm_last_time'] - df['alarm_first_time']).dt.total_seconds() / 3600

# Handle missing values
df['dept_name'] = df['dept_name'].fillna('Unknown Department')
df['address'] = df['address'].fillna('Unknown Address')

print("✅ Data preprocessing completed!")
print(f"📊 Records with deal time: {df['deal_time'].notna().sum()}")
print(f"⏱️  Average response time: {df['response_time_hours'].mean():.2f} hours")
print(f"🎯 Severity distribution:")
print(df['severity_category'].value_counts())

# Display sample of processed data
df.head()

# 📊 Alarm Type Analysis and Distribution

# Create comprehensive alarm type analysis
alarm_summary = df.groupby('alarm_type_name').agg({
    'id': 'count',
    'alarm_frequency': ['sum', 'mean'],
    'alarm_duration_hours': 'mean',
    'response_time_hours': 'mean'
}).round(2)

alarm_summary.columns = ['Total_Alarms', 'Total_Frequency', 'Avg_Frequency', 'Avg_Duration_Hours', 'Avg_Response_Hours']
alarm_summary = alarm_summary.sort_values('Total_Alarms', ascending=False)

print("🎯 Alarm Type Summary:")
print(alarm_summary)

# Create interactive pie chart for alarm type distribution
fig_pie = px.pie(
    values=alarm_summary['Total_Alarms'], 
    names=alarm_summary.index,
    title="🚨 Alarm Type Distribution",
    color_discrete_sequence=px.colors.qualitative.Set3
)
fig_pie.update_traces(textposition='inside', textinfo='percent+label')
fig_pie.update_layout(
    showlegend=True,
    height=500,
    font=dict(size=12)
)
fig_pie.show()

# Create enhanced bar chart with frequency information
fig_bar = px.bar(
    x=alarm_summary.index,
    y=alarm_summary['Total_Alarms'],
    title="📈 Alarm Count by Type",
    labels={'x': 'Alarm Type', 'y': 'Number of Alarms'},
    color=alarm_summary['Total_Alarms'],
    color_continuous_scale='Viridis'
)
fig_bar.update_layout(
    xaxis_tickangle=-45,
    height=500,
    showlegend=False
)
fig_bar.show()

# Create alarm frequency vs count scatter plot
fig_scatter = px.scatter(
    x=alarm_summary['Total_Alarms'],
    y=alarm_summary['Total_Frequency'],
    size=alarm_summary['Avg_Response_Hours'],
    color=alarm_summary.index,
    hover_data=['Avg_Duration_Hours'],
    title="💡 Alarm Count vs Frequency Analysis",
    labels={'x': 'Total Alarms', 'y': 'Total Frequency', 'color': 'Alarm Type'}
)
fig_scatter.update_layout(height=500)
fig_scatter.show()

# 📅 Temporal Analysis - Time-based Patterns

# Daily alarm trends
daily_alarms = df.groupby('alarm_date').agg({
    'id': 'count',
    'alarm_type_name': 'nunique',
    'alarm_area_name': 'nunique'
}).reset_index()
daily_alarms.columns = ['Date', 'Total_Alarms', 'Unique_Types', 'Unique_Areas']

# Create time series plot
fig_timeline = px.line(
    daily_alarms,
    x='Date',
    y='Total_Alarms',
    title="📈 Daily Alarm Trends",
    markers=True,
    line_shape='spline'
)
fig_timeline.add_scatter(
    x=daily_alarms['Date'],
    y=daily_alarms['Unique_Types'] * 100,  # Scale for visibility
    mode='lines+markers',
    name='Unique Types (×100)',
    line=dict(dash='dash', color='red')
)
fig_timeline.update_layout(
    height=500,
    xaxis_title="Date",
    yaxis_title="Number of Alarms",
    hovermode='x unified'
)
fig_timeline.show()

# Hourly patterns heatmap
hourly_data = df.groupby(['alarm_date', 'alarm_hour']).size().reset_index(name='alarm_count')
hourly_pivot = hourly_data.pivot(index='alarm_date', columns='alarm_hour', values='alarm_count').fillna(0)

fig_heatmap = px.imshow(
    hourly_pivot.values,
    labels=dict(x="Hour of Day", y="Date", color="Alarm Count"),
    x=hourly_pivot.columns,
    y=hourly_pivot.index,
    title="🔥 Alarm Intensity Heatmap (Hour vs Date)",
    color_continuous_scale='Reds'
)
fig_heatmap.update_layout(height=600)
fig_heatmap.show()

# Day of week analysis
dow_analysis = df.groupby('alarm_day_of_week').agg({
    'id': 'count',
    'alarm_frequency': 'sum',
    'alarm_duration_hours': 'mean'
}).reset_index()

# Order days of week correctly
day_order = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
dow_analysis['alarm_day_of_week'] = pd.Categorical(dow_analysis['alarm_day_of_week'], categories=day_order, ordered=True)
dow_analysis = dow_analysis.sort_values('alarm_day_of_week')

fig_dow = px.bar(
    dow_analysis,
    x='alarm_day_of_week',
    y='id',
    title="📊 Alarms by Day of Week",
    color='alarm_frequency',
    color_continuous_scale='Plasma'
)
fig_dow.update_layout(
    xaxis_title="Day of Week",
    yaxis_title="Number of Alarms",
    height=500
)
fig_dow.show()

print("📊 Daily Statistics:")
print(daily_alarms.describe())

# 🌍 Geographic and Location Analysis

# Location-based analysis
location_analysis = df.groupby(['alarm_area_name', 'resource_name']).agg({
    'id': 'count',
    'alarm_frequency': 'sum',
    'alarm_type_name': 'nunique',
    'severity_category': lambda x: x.value_counts().index[0]  # Most common severity
}).reset_index()
location_analysis.columns = ['Area', 'Resource', 'Total_Alarms', 'Total_Frequency', 'Unique_Types', 'Primary_Severity']

# Create treemap for hierarchical location view
fig_treemap = px.treemap(
    location_analysis,
    path=['Area', 'Resource'],
    values='Total_Alarms',
    color='Total_Frequency',
    title="🏭 Alarm Distribution by Location (Treemap)",
    color_continuous_scale='RdYlBu_r'
)
fig_treemap.update_layout(height=600)
fig_treemap.show()

# Area comparison
area_comparison = df.groupby('alarm_area_name').agg({
    'id': 'count',
    'alarm_frequency': 'sum',
    'resource_name': 'nunique',
    'alarm_type_name': 'nunique',
    'response_time_hours': 'mean'
}).reset_index()
area_comparison.columns = ['Area', 'Total_Alarms', 'Total_Frequency', 'Unique_Resources', 'Unique_Types', 'Avg_Response_Hours']

# Create radar chart for area comparison
fig_radar = go.Figure()

for area in area_comparison['Area']:
    area_data = area_comparison[area_comparison['Area'] == area]
    fig_radar.add_trace(go.Scatterpolar(
        r=[
            area_data['Total_Alarms'].iloc[0] / area_comparison['Total_Alarms'].max(),
            area_data['Total_Frequency'].iloc[0] / area_comparison['Total_Frequency'].max(),
            area_data['Unique_Resources'].iloc[0] / area_comparison['Unique_Resources'].max(),
            area_data['Unique_Types'].iloc[0] / area_comparison['Unique_Types'].max(),
            (1 - area_data['Avg_Response_Hours'].iloc[0] / area_comparison['Avg_Response_Hours'].max()) if not pd.isna(area_data['Avg_Response_Hours'].iloc[0]) else 0
        ],
        theta=['Alarms', 'Frequency', 'Resources', 'Types', 'Response Speed'],
        fill='toself',
        name=area
    ))

fig_radar.update_layout(
    polar=dict(
        radialaxis=dict(
            visible=True,
            range=[0, 1]
        )),
    showlegend=True,
    title="🎯 Area Performance Comparison (Radar Chart)",
    height=600
)
fig_radar.show()

# Resource hotspot analysis
resource_hotspots = df.groupby(['resource_name', 'alarm_type_name']).size().reset_index(name='alarm_count')
resource_pivot = resource_hotspots.pivot(index='resource_name', columns='alarm_type_name', values='alarm_count').fillna(0)

fig_resource_heatmap = px.imshow(
    resource_pivot.values,
    labels=dict(x="Alarm Type", y="Resource", color="Count"),
    x=resource_pivot.columns,
    y=resource_pivot.index,
    title="🔥 Resource vs Alarm Type Heatmap",
    color_continuous_scale='Oranges'
)
fig_resource_heatmap.update_layout(height=500)
fig_resource_heatmap.show()

print("🏭 Location Analysis Summary:")
print(location_analysis.sort_values('Total_Alarms', ascending=False))

# ⚠️ Severity and Response Analysis

# Severity distribution analysis
severity_analysis = df.groupby(['severity_category', 'alarm_type_name']).agg({
    'id': 'count',
    'alarm_frequency': 'sum',
    'response_time_hours': 'mean',
    'alarm_duration_hours': 'mean'
}).reset_index()

# Create stacked bar chart for severity by type
fig_severity = px.bar(
    severity_analysis,
    x='alarm_type_name',
    y='id',
    color='severity_category',
    title="⚠️ Alarm Severity Distribution by Type",
    labels={'id': 'Number of Alarms', 'alarm_type_name': 'Alarm Type'},
    color_discrete_map={
        'Critical': '#FF0000',
        'High': '#FF6600',
        'Medium': '#FFAA00',
        'Low': '#00AA00',
        'Unknown': '#888888'
    }
)
fig_severity.update_layout(
    xaxis_tickangle=-45,
    height=500,
    legend_title="Severity Level"
)
fig_severity.show()

# Response time analysis (only for alarms with deal_time)
response_data = df[df['deal_time'].notna()].copy()
if not response_data.empty:
    fig_response = px.box(
        response_data,
        x='severity_category',
        y='response_time_hours',
        color='alarm_type_name',
        title="⏱️ Response Time Distribution by Severity",
        labels={'response_time_hours': 'Response Time (Hours)', 'severity_category': 'Severity'}
    )
    fig_response.update_layout(height=500)
    fig_response.show()
    
    # Response time statistics
    response_stats = response_data.groupby('severity_category')['response_time_hours'].describe().round(2)
    print("📊 Response Time Statistics by Severity:")
    print(response_stats)
else:
    print("⚠️ No response time data available for analysis")

# Create bubble chart for alarm frequency vs duration
fig_bubble = px.scatter(
    df,
    x='alarm_frequency',
    y='alarm_duration_hours',
    size='consuming',
    color='severity_category',
    hover_data=['alarm_type_name', 'resource_name'],
    title="🔍 Alarm Frequency vs Duration Analysis",
    labels={'alarm_frequency': 'Alarm Frequency', 'alarm_duration_hours': 'Duration (Hours)'},
    size_max=20
)
fig_bubble.update_layout(height=500)
fig_bubble.show()

# Most problematic alarms (high frequency + long duration)
problematic_alarms = df[
    (df['alarm_frequency'] > df['alarm_frequency'].quantile(0.75)) &
    (df['alarm_duration_hours'] > df['alarm_duration_hours'].quantile(0.75))
].copy()

if not problematic_alarms.empty:
    print(f"🚨 Found {len(problematic_alarms)} problematic alarms (high frequency + long duration)")
    problematic_summary = problematic_alarms.groupby('alarm_type_name').agg({
        'id': 'count',
        'alarm_frequency': 'mean',
        'alarm_duration_hours': 'mean',
        'resource_name': 'nunique'
    }).round(2)
    print(problematic_summary)
else:
    print("✅ No highly problematic alarms found")

# 🔮 Predictive Analysis and Trends

# Prepare data for trend analysis
daily_trends = df.groupby('alarm_date').agg({
    'id': 'count',
    'alarm_frequency': 'sum'
}).reset_index()
daily_trends['date_numeric'] = (daily_trends['alarm_date'] - daily_trends['alarm_date'].min()).dt.days

# Simple linear regression for trend
from scipy import stats

if len(daily_trends) > 3:
    slope, intercept, r_value, p_value, std_err = stats.linregress(daily_trends['date_numeric'], daily_trends['id'])
    
    # Create trend line
    daily_trends['trend'] = slope * daily_trends['date_numeric'] + intercept
    
    # Create forecast
    future_days = 7
    future_dates = pd.date_range(start=daily_trends['alarm_date'].max() + pd.Timedelta(days=1), 
                                periods=future_days, freq='D')
    future_numeric = [(date - daily_trends['alarm_date'].min()).days for date in future_dates]
    future_trend = [slope * day + intercept for day in future_numeric]
    
    # Visualization
    fig_forecast = go.Figure()
    
    # Historical data
    fig_forecast.add_trace(go.Scatter(
        x=daily_trends['alarm_date'],
        y=daily_trends['id'],
        mode='lines+markers',
        name='Historical Alarms',
        line=dict(color='blue')
    ))
    
    # Trend line
    fig_forecast.add_trace(go.Scatter(
        x=daily_trends['alarm_date'],
        y=daily_trends['trend'],
        mode='lines',
        name=f'Trend (R²={r_value**2:.3f})',
        line=dict(color='red', dash='dash')
    ))
    
    # Forecast
    fig_forecast.add_trace(go.Scatter(
        x=future_dates,
        y=future_trend,
        mode='lines+markers',
        name='7-Day Forecast',
        line=dict(color='green', dash='dot')
    ))
    
    fig_forecast.update_layout(
        title="📈 Alarm Trend Analysis and 7-Day Forecast",
        xaxis_title="Date",
        yaxis_title="Number of Alarms",
        height=500,
        hovermode='x unified'
    )
    fig_forecast.show()
    
    print(f"📊 Trend Analysis Results:")
    print(f"   • Slope: {slope:.2f} alarms/day")
    print(f"   • R-squared: {r_value**2:.3f}")
    print(f"   • P-value: {p_value:.4f}")
    
    if slope > 0:
        print("   • 📈 Increasing trend detected")
    elif slope < 0:
        print("   • 📉 Decreasing trend detected")
    else:
        print("   • ➡️ Stable trend")
        
    print(f"   • Predicted alarms for next 7 days: {sum(future_trend):.0f}")

# Alarm pattern analysis by hour
hourly_patterns = df.groupby('alarm_hour').agg({
    'id': 'count',
    'alarm_frequency': 'sum'
}).reset_index()

# Create polar chart for hourly patterns
fig_polar = px.line_polar(
    hourly_patterns,
    r='id',
    theta='alarm_hour',
    line_close=True,
    title="🕐 24-Hour Alarm Pattern (Polar Chart)",
    range_r=[0, hourly_patterns['id'].max()]
)
fig_polar.update_traces(fill='toself', fillcolor='rgba(255,0,0,0.1)')
fig_polar.update_layout(height=500)
fig_polar.show()

# Peak hours analysis
peak_hours = hourly_patterns.nlargest(5, 'id')
print("🕐 Top 5 Peak Hours:")
for _, row in peak_hours.iterrows():
    print(f"   • {row['alarm_hour']:02d}:00 - {row['id']} alarms")

# Alarm clustering by type and time
alarm_clustering = df.groupby(['alarm_type_name', 'alarm_hour']).size().reset_index(name='count')
alarm_matrix = alarm_clustering.pivot(index='alarm_type_name', columns='alarm_hour', values='count').fillna(0)

fig_cluster = px.imshow(
    alarm_matrix.values,
    labels=dict(x="Hour of Day", y="Alarm Type", color="Count"),
    x=alarm_matrix.columns,
    y=alarm_matrix.index,
    title="🎯 Alarm Type vs Hour Clustering",
    color_continuous_scale='Viridis'
)
fig_cluster.update_layout(height=500)
fig_cluster.show()

# 📋 Dashboard Summary and Actionable Insights

# Create comprehensive summary
total_alarms = len(df)
date_range = (df['alarm_first_time'].max() - df['alarm_first_time'].min()).days
avg_daily_alarms = total_alarms / date_range if date_range > 0 else total_alarms
most_common_type = df['alarm_type_name'].value_counts().index[0]
most_problematic_area = df['alarm_area_name'].value_counts().index[0]

# Key metrics
print("="*60)
print("🎯 INDUSTRIAL SAFETY ALARM DASHBOARD SUMMARY")
print("="*60)
print(f"📊 Total Alarms Analyzed: {total_alarms:,}")
print(f"📅 Analysis Period: {date_range} days")
print(f"📈 Average Daily Alarms: {avg_daily_alarms:.1f}")
print(f"⚠️  Most Common Alarm Type: {most_common_type}")
print(f"🏭 Most Problematic Area: {most_problematic_area}")
print(f"🔍 Unique Alarm Types: {df['alarm_type_name'].nunique()}")
print(f"📍 Monitoring Locations: {df['resource_name'].nunique()}")

# Top insights
print("\n🔍 KEY INSIGHTS:")
print("-" * 40)

# 1. Alarm type insights
type_counts = df['alarm_type_name'].value_counts()
print(f"1. 🚨 '{type_counts.index[0]}' accounts for {type_counts.iloc[0]/total_alarms*100:.1f}% of all alarms")

# 2. Time pattern insights
busiest_hour = df['alarm_hour'].value_counts().index[0]
busiest_day = df['alarm_day_of_week'].value_counts().index[0]
print(f"2. 🕐 Peak alarm time: {busiest_hour:02d}:00 on {busiest_day}s")

# 3. Geographic insights
area_counts = df['alarm_area_name'].value_counts()
print(f"3. 🏭 '{area_counts.index[0]}' has {area_counts.iloc[0]/total_alarms*100:.1f}% of all alarms")

# 4. Severity insights
severity_counts = df['severity_category'].value_counts()
print(f"4. ⚠️  {severity_counts.iloc[0]} alarms ({severity_counts.iloc[0]/total_alarms*100:.1f}%) are '{severity_counts.index[0]}' severity")

# 5. Response time insights
if not df['response_time_hours'].isna().all():
    avg_response = df['response_time_hours'].mean()
    print(f"5. ⏱️  Average response time: {avg_response:.1f} hours")
else:
    print("5. ⏱️  Response time data not available")

# Recommendations
print("\n💡 RECOMMENDATIONS:")
print("-" * 40)
print("1. 🎯 Focus on reducing '{most_common_type}' alarms through preventive measures")
print("2. 🕐 Increase monitoring during peak hours (especially around {busiest_hour:02d}:00)")
print("3. 🏭 Implement enhanced safety protocols in '{most_problematic_area}'")
print("4. 📊 Set up automated alerts for high-frequency alarm patterns")
print("5. 🔄 Review and optimize alarm sensitivity settings")

# Create final summary visualization
fig_summary = make_subplots(
    rows=2, cols=2,
    subplot_titles=('Daily Alarm Trend', 'Alarm Type Distribution', 'Hourly Pattern', 'Area Comparison'),
    specs=[[{"secondary_y": False}, {"type": "pie"}],
           [{"type": "polar"}, {"secondary_y": False}]]
)

# Daily trend
fig_summary.add_trace(
    go.Scatter(x=daily_trends['alarm_date'], y=daily_trends['id'], name='Daily Alarms'),
    row=1, col=1
)

# Alarm type pie
fig_summary.add_trace(
    go.Pie(labels=type_counts.index[:5], values=type_counts.values[:5], name="Types"),
    row=1, col=2
)

# Hourly polar
fig_summary.add_trace(
    go.Scatterpolar(r=hourly_patterns['id'], theta=hourly_patterns['alarm_hour'], 
                    fill='toself', name='Hourly Pattern'),
    row=2, col=1
)

# Area comparison
fig_summary.add_trace(
    go.Bar(x=area_counts.index, y=area_counts.values, name='Area Alarms'),
    row=2, col=2
)

fig_summary.update_layout(
    title_text="📊 Alarm Analysis Summary Dashboard",
    showlegend=False,
    height=800
)
fig_summary.show()

print("\n✅ Dashboard analysis complete!")
print("🔄 Run individual cells above to explore specific aspects in detail.")
print("📈 Use the interactive charts to drill down into the data.")
print("="*60)