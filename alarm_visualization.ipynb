# Import Required Libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.figure_factory as ff
from sqlalchemy import create_engine
import psycopg2
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Set up plotting style
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

print("📚 All libraries imported successfully!")
print("🎨 Plotting styles configured")

# Database connection parameters
DATABASE_URI = "*********************************************/Industrial_Internet"

# Create SQLAlchemy engine
try:
    engine = create_engine(DATABASE_URI)
    print("🔗 Database connection established successfully!")
    
    # Test connection
    with engine.connect() as conn:
        result = conn.execute("SELECT version()")
        version = result.fetchone()[0]
        print(f"📊 Connected to: {version[:50]}...")
        
except Exception as e:
    print(f"❌ Connection failed: {e}")
    print("Please check your database credentials and network connectivity.")

# SQL query to fetch recent alarm data
query = """
SELECT 
    id,
    alarm_no,
    alarm_way,
    alarm_type,
    alarm_type_name,
    alarm_level,
    alarm_level_refer,
    alarm_area_id,
    alarm_area_name,
    address,
    resource_id,
    resource_name,
    alarm_first_time,
    alarm_last_time,
    dept_name,
    alarm_frequency,
    status,
    deal_result_view,
    deal_user_name,
    deal_time,
    deal_way_view,
    consuming,
    station_flag,
    floor_level
FROM alarm.alm_alarm_history 
WHERE alarm_no > '20250620'
ORDER BY alarm_first_time DESC
"""

print("🔄 Executing query to fetch alarm data...")
try:
    # Load data into pandas DataFrame
    df = pd.read_sql_query(query, engine)
    
    print(f"✅ Successfully loaded {len(df):,} records")
    print(f"📅 Date range: {df['alarm_first_time'].min()} to {df['alarm_first_time'].max()}")
    print(f"💾 Memory usage: {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB")
    
except Exception as e:
    print(f"❌ Query failed: {e}")
    df = pd.DataFrame()  # Create empty DataFrame as fallback

# Data inspection and basic statistics
print("📋 DataFrame Info:")
print(f"Shape: {df.shape}")
print(f"Columns: {list(df.columns)}")
print("\n🔍 Data Types:")
print(df.dtypes)

print("\n📊 Basic Statistics:")
print(df.describe())

print("\n🕳️ Missing Values:")
missing_values = df.isnull().sum()
print(missing_values[missing_values > 0])

# Data preprocessing
if not df.empty:
    # Convert timestamp columns to datetime
    df['alarm_first_time'] = pd.to_datetime(df['alarm_first_time'])
    df['alarm_last_time'] = pd.to_datetime(df['alarm_last_time'])
    df['deal_time'] = pd.to_datetime(df['deal_time'])
    
    # Create additional time-based features
    df['alarm_date'] = df['alarm_first_time'].dt.date
    df['alarm_hour'] = df['alarm_first_time'].dt.hour
    df['alarm_weekday'] = df['alarm_first_time'].dt.day_name()
    df['alarm_month'] = df['alarm_first_time'].dt.month
    
    # Calculate processing time in hours
    df['processing_time_hours'] = (df['deal_time'] - df['alarm_first_time']).dt.total_seconds() / 3600
    
    print("\n✅ Data preprocessing completed!")
    print(f"📅 Unique dates: {df['alarm_date'].nunique()}")
    print(f"🏢 Unique locations: {df['resource_name'].nunique()}")
    print(f"⚠️ Unique alarm types: {df['alarm_type'].nunique()}")
else:
    print("❌ No data available for analysis")

# 5.1 Alarm Type Distribution
if not df.empty:
    # Calculate alarm type statistics
    alarm_type_counts = df.groupby(['alarm_type', 'alarm_type_name']).size().reset_index(name='count')
    alarm_type_counts = alarm_type_counts.sort_values('count', ascending=False)
    
    # Create interactive pie chart
    fig_pie = px.pie(
        alarm_type_counts, 
        values='count', 
        names='alarm_type_name',
        title='🚨 Alarm Type Distribution (Since June 20, 2025)',
        hover_data=['alarm_type'],
        color_discrete_sequence=px.colors.qualitative.Set3
    )
    
    fig_pie.update_traces(
        textposition='inside', 
        textinfo='percent+label',
        hovertemplate='<b>%{label}</b><br>Count: %{value}<br>Percentage: %{percent}<extra></extra>'
    )
    
    fig_pie.update_layout(
        height=500,
        showlegend=True,
        title_x=0.5,
        font=dict(size=12)
    )
    
    fig_pie.show()
    
    # Print statistics
    print("📊 Alarm Type Statistics:")
    for _, row in alarm_type_counts.iterrows():
        percentage = (row['count'] / len(df)) * 100
        print(f"  • {row['alarm_type_name']}: {row['count']:,} alarms ({percentage:.1f}%)")
    
    print(f"\n🎯 Total alarms analyzed: {len(df):,}")
else:
    print("❌ No data available for visualization")

# 5.2 Temporal Patterns - Date vs Hour Heatmap
if not df.empty:
    # Create hourly heatmap data
    heatmap_data = df.groupby(['alarm_date', 'alarm_hour']).size().reset_index(name='count')
    
    # Pivot for heatmap
    heatmap_pivot = heatmap_data.pivot(index='alarm_date', columns='alarm_hour', values='count').fillna(0)
    
    # Create interactive heatmap
    fig_heatmap = go.Figure(data=go.Heatmap(
        z=heatmap_pivot.values,
        x=[f"{i:02d}:00" for i in range(24)],
        y=[str(date) for date in heatmap_pivot.index],
        colorscale='YlOrRd',
        hoverongaps=False,
        hovertemplate='Date: %{y}<br>Hour: %{x}<br>Alarms: %{z}<extra></extra>'
    ))
    
    fig_heatmap.update_layout(
        title='📅 Alarm Temporal Pattern Heatmap (Date vs Hour)',
        xaxis_title='Hour of Day',
        yaxis_title='Date',
        height=600,
        font=dict(size=12)
    )
    
    fig_heatmap.show()
    
    # Daily alarm summary
    daily_summary = df.groupby('alarm_date').agg({
        'id': 'count',
        'alarm_hour': ['min', 'max']
    }).round(2)
    daily_summary.columns = ['Total_Alarms', 'First_Hour', 'Last_Hour']
    daily_summary = daily_summary.sort_values('Total_Alarms', ascending=False)
    
    print("📈 Top 5 Days by Alarm Count:")
    print(daily_summary.head())
    
    # Hourly pattern analysis
    hourly_pattern = df.groupby('alarm_hour').size().sort_index()
    peak_hour = hourly_pattern.idxmax()
    peak_count = hourly_pattern.max()
    
    print(f"\n⏰ Peak alarm hour: {peak_hour:02d}:00 with {peak_count:,} alarms")
    print(f"🌙 Quietest hour: {hourly_pattern.idxmin():02d}:00 with {hourly_pattern.min():,} alarms")
else:
    print("❌ No data available for temporal analysis")

# 5.3 Location-Based Analysis
if not df.empty:
    # Top monitoring locations
    location_stats = df.groupby(['resource_name', 'alarm_area_name']).agg({
        'id': 'count',
        'alarm_type': lambda x: x.value_counts().index[0],  # Most common alarm type
        'processing_time_hours': 'mean'
    }).reset_index()
    
    location_stats.columns = ['Resource', 'Area', 'Total_Alarms', 'Main_Alarm_Type', 'Avg_Processing_Hours']
    location_stats = location_stats.sort_values('Total_Alarms', ascending=False)
    
    # Create interactive bar chart for top 10 locations
    top_locations = location_stats.head(10)
    
    fig_locations = px.bar(
        top_locations,
        x='Total_Alarms',
        y='Resource',
        color='Area',
        title='🏢 Top 10 Monitoring Locations by Alarm Count',
        labels={'Total_Alarms': 'Number of Alarms', 'Resource': 'Monitoring Point'},
        orientation='h',
        hover_data=['Main_Alarm_Type', 'Avg_Processing_Hours']
    )
    
    fig_locations.update_layout(
        height=500,
        yaxis={'categoryorder': 'total ascending'},
        font=dict(size=12)
    )
    
    fig_locations.show()
    
    print("🏆 Top 5 Most Active Monitoring Points:")
    for i, row in top_locations.head().iterrows():
        print(f"  {row.name + 1}. {row['Resource']} ({row['Area']}): {row['Total_Alarms']:,} alarms")
        print(f"     Main type: {row['Main_Alarm_Type']}, Avg processing: {row['Avg_Processing_Hours']:.1f}h")
        print()
else:
    print("❌ No data available for location analysis")

# 5.4 Time Series Analysis - Daily Alarm Trends
if not df.empty:
    # Daily alarm counts by type
    daily_by_type = df.groupby(['alarm_date', 'alarm_type_name']).size().reset_index(name='count')
    
    # Create interactive time series
    fig_timeseries = px.line(
        daily_by_type,
        x='alarm_date',
        y='count',
        color='alarm_type_name',
        title='📊 Daily Alarm Trends by Type',
        labels={'alarm_date': 'Date', 'count': 'Number of Alarms', 'alarm_type_name': 'Alarm Type'},
        markers=True
    )
    
    fig_timeseries.update_layout(
        height=500,
        xaxis_title='Date',
        yaxis_title='Number of Alarms',
        legend_title='Alarm Type',
        font=dict(size=12)
    )
    
    fig_timeseries.show()
    
    # Weekly pattern analysis
    weekly_pattern = df.groupby('alarm_weekday').size().reindex([
        'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'
    ]).fillna(0)
    
    fig_weekly = px.bar(
        x=weekly_pattern.index,
        y=weekly_pattern.values,
        title='📅 Alarm Distribution by Day of Week',
        labels={'x': 'Day of Week', 'y': 'Number of Alarms'},
        color=weekly_pattern.values,
        color_continuous_scale='viridis'
    )
    
    fig_weekly.update_layout(
        height=400,
        showlegend=False,
        font=dict(size=12)
    )
    
    fig_weekly.show()
    
    print("📆 Weekly Pattern Summary:")
    for day, count in weekly_pattern.items():
        print(f"  • {day}: {int(count):,} alarms")
else:
    print("❌ No data available for time series analysis")