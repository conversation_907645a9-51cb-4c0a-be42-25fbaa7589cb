from mcp.server.fastmcp import FastMCP
import psycopg2
import psycopg2.extras
import json
from typing import Optional, List, Dict, Any

mcp = FastMCP("PostgreSQLMCP", port=9010, host="0.0.0.0")

# PostgreSQL连接配置
DB_CONFIG = {
    "host": "localhost",
    "port": 5432,
    "user": "postgres",
    "password": "123456",
    "database": "mcp_demo"
}

def get_connection():
    """获取PostgreSQL数据库连接"""
    return psycopg2.connect(**DB_CONFIG)

@mcp.tool()
def pg_query(sql: str, params: Optional[List] = None) -> List[Dict[str, Any]]:
    """
    执行PostgreSQL只读查询并返回结果。
    参数:
        sql: 要执行的SQL查询语句（只支持SELECT语句）
        params: SQL参数列表（可选），用于参数化查询
    返回:
        查询结果的字典列表，每行数据为一个字典
    """
    # 安全检查：只允许SELECT查询
    sql_upper = sql.strip().upper()
    if not sql_upper.startswith('SELECT'):
        raise ValueError("只支持SELECT查询语句")
    
    # 检查是否包含危险关键字
    dangerous_keywords = ['INSERT', 'UPDATE', 'DELETE', 'DROP', 'CREATE', 'ALTER', 'TRUNCATE']
    for keyword in dangerous_keywords:
        if keyword in sql_upper:
            raise ValueError(f"查询中不能包含 {keyword} 关键字")
    
    conn = get_connection()
    try:
        # 设置事务为只读模式
        conn.set_session(readonly=True)
        cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        
        if params:
            cursor.execute(sql, params)
        else:
            cursor.execute(sql)
        
        result = cursor.fetchall()
        # 转换为普通字典列表
        return [dict(row) for row in result]
        
    except Exception as e:
        raise Exception(f"查询执行失败: {str(e)}")
    finally:
        cursor.close()
        conn.close()

@mcp.tool()
def pg_explain(sql: str, params: Optional[List] = None) -> Dict[str, Any]:
    """
    获取PostgreSQL查询的执行计划分析。
    参数:
        sql: 要分析的SQL查询语句（只支持SELECT语句）
        params: SQL参数列表（可选）
    返回:
        包含执行计划的详细信息字典
    """
    # 安全检查：只允许SELECT查询
    sql_upper = sql.strip().upper()
    if not sql_upper.startswith('SELECT'):
        raise ValueError("只支持SELECT查询语句")
    
    conn = get_connection()
    try:
        conn.set_session(readonly=True)
        cursor = conn.cursor()
        
        # 构建EXPLAIN查询
        explain_sql = f"EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON) {sql}"
        
        if params:
            cursor.execute(explain_sql, params)
        else:
            cursor.execute(explain_sql)
        
        result = cursor.fetchone()[0]
        return result[0] if result else {}
        
    except Exception as e:
        raise Exception(f"执行计划分析失败: {str(e)}")
    finally:
        cursor.close()
        conn.close()

@mcp.tool()
def get_table_info(schema_name: str = "public") -> List[Dict[str, Any]]:
    """
    获取指定模式下所有表的基本信息。
    参数:
        schema_name: 模式名称，默认为public
    返回:
        表信息列表，包含表名、注释、行数等
    """
    sql = """
    SELECT 
        t.table_name,
        obj_description(c.oid) as table_comment,
        (SELECT COUNT(*) FROM information_schema.columns 
         WHERE table_schema = t.table_schema AND table_name = t.table_name) as column_count,
        pg_stat_get_tuples_fetched(c.oid) as rows_fetched,
        pg_stat_get_tuples_inserted(c.oid) as rows_inserted
    FROM information_schema.tables t
    LEFT JOIN pg_class c ON c.relname = t.table_name
    LEFT JOIN pg_namespace n ON n.oid = c.relnamespace
    WHERE t.table_schema = %s 
    AND t.table_type = 'BASE TABLE'
    ORDER BY t.table_name
    """
    
    return pg_query(sql, [schema_name])

@mcp.tool()
def get_table_columns(table_name: str, schema_name: str = "public") -> List[Dict[str, Any]]:
    """
    获取指定表的列信息。
    参数:
        table_name: 表名
        schema_name: 模式名称，默认为public
    返回:
        列信息列表，包含列名、数据类型、是否可空等
    """
    sql = """
    SELECT 
        column_name,
        data_type,
        is_nullable,
        column_default,
        character_maximum_length,
        numeric_precision,
        numeric_scale,
        col_description(pgc.oid, c.ordinal_position) as column_comment
    FROM information_schema.columns c
    LEFT JOIN pg_class pgc ON pgc.relname = c.table_name
    LEFT JOIN pg_namespace pgn ON pgn.oid = pgc.relnamespace
    WHERE c.table_schema = %s 
    AND c.table_name = %s
    ORDER BY c.ordinal_position
    """
    
    return pg_query(sql, [schema_name, table_name])

@mcp.tool()
def get_table_indexes(table_name: str, schema_name: str = "public") -> List[Dict[str, Any]]:
    """
    获取指定表的索引信息。
    参数:
        table_name: 表名
        schema_name: 模式名称，默认为public
    返回:
        索引信息列表
    """
    sql = """
    SELECT 
        i.indexname as index_name,
        i.indexdef as index_definition,
        am.amname as index_type,
        idx.indisunique as is_unique,
        idx.indisprimary as is_primary
    FROM pg_indexes i
    LEFT JOIN pg_class c ON c.relname = i.tablename
    LEFT JOIN pg_index idx ON idx.indexrelid = (
        SELECT oid FROM pg_class WHERE relname = i.indexname
    )
    LEFT JOIN pg_am am ON am.oid = (
        SELECT relam FROM pg_class WHERE relname = i.indexname
    )
    WHERE i.schemaname = %s 
    AND i.tablename = %s
    ORDER BY i.indexname
    """
    
    return pg_query(sql, [schema_name, table_name])

@mcp.tool()
def get_database_stats() -> Dict[str, Any]:
    """
    获取数据库统计信息。
    返回:
        数据库统计信息字典
    """
    stats_sql = """
    SELECT 
        (SELECT COUNT(*) FROM information_schema.tables 
         WHERE table_schema = 'public' AND table_type = 'BASE TABLE') as table_count,
        (SELECT COUNT(*) FROM information_schema.views 
         WHERE table_schema = 'public') as view_count,
        (SELECT COUNT(*) FROM information_schema.routines 
         WHERE routine_schema = 'public') as function_count,
        pg_database_size(current_database()) as database_size_bytes,
        current_database() as database_name,
        version() as postgresql_version
    """
    
    result = pg_query(stats_sql)
    if result:
        stats = result[0]
        # 转换字节为可读格式
        size_bytes = stats.get('database_size_bytes', 0)
        if size_bytes:
            if size_bytes > 1024**3:
                stats['database_size'] = f"{size_bytes / (1024**3):.2f} GB"
            elif size_bytes > 1024**2:
                stats['database_size'] = f"{size_bytes / (1024**2):.2f} MB"
            elif size_bytes > 1024:
                stats['database_size'] = f"{size_bytes / 1024:.2f} KB"
            else:
                stats['database_size'] = f"{size_bytes} bytes"
        return stats
    return {}

@mcp.tool()
def search_tables(keyword: str, schema_name: str = "public") -> List[Dict[str, Any]]:
    """
    根据关键字搜索表名或表注释。
    参数:
        keyword: 搜索关键字
        schema_name: 模式名称，默认为public
    返回:
        匹配的表信息列表
    """
    sql = """
    SELECT 
        t.table_name,
        obj_description(c.oid) as table_comment
    FROM information_schema.tables t
    LEFT JOIN pg_class c ON c.relname = t.table_name
    WHERE t.table_schema = %s 
    AND t.table_type = 'BASE TABLE'
    AND (
        t.table_name ILIKE %s 
        OR obj_description(c.oid) ILIKE %s
    )
    ORDER BY t.table_name
    """
    
    search_pattern = f"%{keyword}%"
    return pg_query(sql, [schema_name, search_pattern, search_pattern])

if __name__ == "__main__":
    mcp.run(transport="sse")  # 使用SSE传输
