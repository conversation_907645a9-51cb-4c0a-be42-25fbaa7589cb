from mcp.server.fastmcp import FastMCP
import psycopg2
import psycopg2.extras
import json
from typing import Optional, List, Dict, Any

mcp = FastMCP("PostgreSQLMCP", port=9010, host="0.0.0.0")

# PostgreSQL连接配置
DB_CONFIG = {
    "host": "************",
    "port": 5432,
    "user": "admin",
    "password": "greatech",
    "database": "Industrial_Internet",
}

def get_connection():
    """获取PostgreSQL数据库连接"""
    return psycopg2.connect(**DB_CONFIG)

@mcp.tool()
def pg_query(sql: str, params: Optional[List] = None) -> List[Dict[str, Any]]:
    """
    执行PostgreSQL只读查询并返回结果。
    参数:
        sql: 要执行的SQL查询语句（只支持SELECT语句）
        params: SQL参数列表（可选），用于参数化查询
    返回:
        查询结果的字典列表，每行数据为一个字典
    """
    # 安全检查：只允许SELECT查询
    sql_upper = sql.strip().upper()
    if not sql_upper.startswith('SELECT'):
        raise ValueError("只支持SELECT查询语句")
    
    # 检查是否包含危险关键字（但排除在字符串和注释中的情况）
    dangerous_keywords = ['INSERT INTO', 'UPDATE SET', 'DELETE FROM', 'DROP TABLE', 'CREATE TABLE', 'ALTER TABLE', 'TRUNCATE TABLE']
    for keyword in dangerous_keywords:
        if keyword in sql_upper:
            raise ValueError(f"查询中不能包含 {keyword} 关键字")
    
    conn = get_connection()
    try:
        # 设置事务为只读模式
        conn.set_session(readonly=True)
        cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)
        
        if params:
            cursor.execute(sql, params)
        else:
            cursor.execute(sql)
        
        result = cursor.fetchall()
        # 转换为普通字典列表
        return [dict(row) for row in result]
        
    except Exception as e:
        raise Exception(f"查询执行失败: {str(e)}")
    finally:
        cursor.close()
        conn.close()

@mcp.tool()
def pg_explain(sql: str, params: Optional[List] = None) -> Dict[str, Any]:
    """
    获取PostgreSQL查询的执行计划分析。
    参数:
        sql: 要分析的SQL查询语句（只支持SELECT语句）
        params: SQL参数列表（可选）
    返回:
        包含执行计划的详细信息字典
    """
    # 安全检查：只允许SELECT查询
    sql_upper = sql.strip().upper()
    if not sql_upper.startswith('SELECT'):
        raise ValueError("只支持SELECT查询语句")
    
    conn = get_connection()
    try:
        conn.set_session(readonly=True)
        cursor = conn.cursor()
        
        # 构建EXPLAIN查询
        explain_sql = f"EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON) {sql}"
        
        if params:
            cursor.execute(explain_sql, params)
        else:
            cursor.execute(explain_sql)
        
        result = cursor.fetchone()[0]
        return result[0] if result else {}
        
    except Exception as e:
        raise Exception(f"执行计划分析失败: {str(e)}")
    finally:
        cursor.close()
        conn.close()

@mcp.tool()
def get_table_info(schema_name: str) -> List[Dict[str, Any]]:
    """
    获取指定模式下所有表的基本信息。
    """
    sql = """
    SELECT
        t.table_name,
        COALESCE(obj_description(c.oid), '') as table_comment,
        (SELECT COUNT(*) FROM information_schema.columns
         WHERE table_schema = t.table_schema AND table_name = t.table_name) as column_count,
        COALESCE(pg_stat_get_tuples_fetched(c.oid), 0) as rows_fetched,
        COALESCE(pg_stat_get_tuples_returned(c.oid), 0) as rows_returned
    FROM information_schema.tables t
    LEFT JOIN pg_namespace n ON n.nspname = t.table_schema
    LEFT JOIN pg_class c ON c.relname = t.table_name AND c.relnamespace = n.oid
    WHERE t.table_schema = %s
    AND t.table_type = 'BASE TABLE'
    ORDER BY t.table_name
    """
    return pg_query(sql, [schema_name])


@mcp.tool()
def list_tables(schema_name: str = "public") -> List[str]:
    """
    获取指定模式下所有表的名称列表。
    参数:
        schema_name: 模式名称，默认为public
    返回:
        表名称列表
    """
    sql = """
    SELECT table_name
    FROM information_schema.tables
    WHERE table_schema = %s
    AND table_type = 'BASE TABLE'
    ORDER BY table_name
    """

    result = pg_query(sql, [schema_name])
    return [row['table_name'] for row in result]

@mcp.tool()
def get_all_schemas() -> List[Dict[str, Any]]:
    """
    获取数据库中所有可用的模式（schema）。
    返回:
        模式信息列表，包含模式名和描述
    """
    sql = """
    SELECT
        schema_name,
        schema_owner,
        CASE
            WHEN schema_name = 'public' THEN '默认公共模式'
            WHEN schema_name LIKE 'pg_%' THEN '系统模式'
            WHEN schema_name = 'information_schema' THEN '信息模式'
            ELSE '用户自定义模式'
        END as schema_description
    FROM information_schema.schemata
    WHERE schema_name NOT IN ('pg_toast', 'pg_temp_1', 'pg_toast_temp_1')
    ORDER BY
        CASE
            WHEN schema_name = 'public' THEN 1
            WHEN schema_name LIKE 'pg_%' THEN 3
            WHEN schema_name = 'information_schema' THEN 3
            ELSE 2
        END,
        schema_name
    """

    return pg_query(sql)

@mcp.tool()
def get_table_columns(table_name: str, schema_name: str = "public") -> List[Dict[str, Any]]:
    """
    获取指定表的列信息。
    参数:
        table_name: 表名
        schema_name: 模式名称，默认为public
    返回:
        列信息列表，包含列名、数据类型、是否可空等
    """
    sql = """
    SELECT 
        column_name,
        data_type,
        is_nullable,
        column_default,
        character_maximum_length,
        numeric_precision,
        numeric_scale,
        col_description(pgc.oid, c.ordinal_position) as column_comment
    FROM information_schema.columns c
    LEFT JOIN pg_class pgc ON pgc.relname = c.table_name
    LEFT JOIN pg_namespace pgn ON pgn.oid = pgc.relnamespace
    WHERE c.table_schema = %s 
    AND c.table_name = %s
    ORDER BY c.ordinal_position
    """
    
    return pg_query(sql, [schema_name, table_name])

@mcp.tool()
def get_table_indexes(table_name: str, schema_name: str = "public") -> List[Dict[str, Any]]:
    """
    获取指定表的索引信息。
    参数:
        table_name: 表名
        schema_name: 模式名称，默认为public
    返回:
        索引信息列表
    """
    sql = """
    SELECT 
        i.indexname as index_name,
        i.indexdef as index_definition,
        am.amname as index_type,
        idx.indisunique as is_unique,
        idx.indisprimary as is_primary
    FROM pg_indexes i
    LEFT JOIN pg_class c ON c.relname = i.tablename
    LEFT JOIN pg_index idx ON idx.indexrelid = (
        SELECT oid FROM pg_class WHERE relname = i.indexname
    )
    LEFT JOIN pg_am am ON am.oid = (
        SELECT relam FROM pg_class WHERE relname = i.indexname
    )
    WHERE i.schemaname = %s 
    AND i.tablename = %s
    ORDER BY i.indexname
    """
    
    return pg_query(sql, [schema_name, table_name])

@mcp.tool()
def get_database_stats() -> Dict[str, Any]:
    """
    获取数据库统计信息。
    返回:
        数据库统计信息字典
    """
    stats_sql = """
    SELECT 
        (SELECT COUNT(*) FROM information_schema.tables 
         WHERE table_schema = 'public' AND table_type = 'BASE TABLE') as table_count,
        (SELECT COUNT(*) FROM information_schema.views 
         WHERE table_schema = 'public') as view_count,
        (SELECT COUNT(*) FROM information_schema.routines 
         WHERE routine_schema = 'public') as function_count,
        pg_database_size(current_database()) as database_size_bytes,
        current_database() as database_name,
        version() as postgresql_version
    """
    
    result = pg_query(stats_sql)
    if result:
        stats = result[0]
        # 转换字节为可读格式
        size_bytes = stats.get('database_size_bytes', 0)
        if size_bytes:
            if size_bytes > 1024**3:
                stats['database_size'] = f"{size_bytes / (1024**3):.2f} GB"
            elif size_bytes > 1024**2:
                stats['database_size'] = f"{size_bytes / (1024**2):.2f} MB"
            elif size_bytes > 1024:
                stats['database_size'] = f"{size_bytes / 1024:.2f} KB"
            else:
                stats['database_size'] = f"{size_bytes} bytes"
        return stats
    return {}

@mcp.tool()
def search_tables(keyword: str, schema_name: str = "public") -> List[Dict[str, Any]]:
    """
    根据关键字搜索表名或表注释。
    参数:
        keyword: 搜索关键字
        schema_name: 模式名称，默认为public
    返回:
        匹配的表信息列表
    """
    sql = """
    SELECT
        t.table_name,
        COALESCE(obj_description(c.oid), '') as table_comment,
        t.table_schema
    FROM information_schema.tables t
    LEFT JOIN pg_namespace n ON n.nspname = t.table_schema
    LEFT JOIN pg_class c ON c.relname = t.table_name AND c.relnamespace = n.oid
    WHERE t.table_schema = %s
    AND t.table_type = 'BASE TABLE'
    AND (
        t.table_name ILIKE %s
        OR COALESCE(obj_description(c.oid), '') ILIKE %s
    )
    ORDER BY t.table_name
    """

    search_pattern = f"%{keyword}%"
    return pg_query(sql, [schema_name, search_pattern, search_pattern])

@mcp.tool()
def analyze_table_performance(table_name: str, schema_name: str = "public") -> Dict[str, Any]:
    """
    分析表的性能统计信息。
    参数:
        table_name: 表名
        schema_name: 模式名称，默认为public
    返回:
        表的性能统计信息
    """
    sql = """
    SELECT
        schemaname,
        tablename,
        attname,
        n_distinct,
        most_common_vals,
        most_common_freqs,
        histogram_bounds,
        correlation
    FROM pg_stats
    WHERE schemaname = %s AND tablename = %s
    ORDER BY attname
    """

    stats_result = pg_query(sql, [schema_name, table_name])

    # 获取表的基本统计信息
    table_stats_sql = """
    SELECT
        pg_size_pretty(pg_total_relation_size(c.oid)) as total_size,
        pg_size_pretty(pg_relation_size(c.oid)) as table_size,
        pg_size_pretty(pg_total_relation_size(c.oid) - pg_relation_size(c.oid)) as index_size,
        reltuples::bigint as estimated_rows,
        relpages as pages
    FROM pg_class c
    JOIN pg_namespace n ON n.oid = c.relnamespace
    WHERE n.nspname = %s AND c.relname = %s
    """

    table_stats = pg_query(table_stats_sql, [schema_name, table_name])

    return {
        "table_statistics": table_stats[0] if table_stats else {},
        "column_statistics": stats_result
    }

@mcp.tool()
def suggest_indexes(table_name: str, schema_name: str = "public") -> Dict[str, Any]:
    """
    基于查询模式建议索引优化。
    参数:
        table_name: 表名
        schema_name: 模式名称，默认为public
    返回:
        索引建议列表
    """
    # 检查当前索引
    current_indexes_sql = """
    SELECT
        i.indexname,
        i.indexdef,
        pg_size_pretty(pg_relation_size(quote_ident(i.schemaname)||'.'||quote_ident(i.indexname))) as index_size,
        s.idx_scan as times_used,
        s.idx_tup_read as tuples_read,
        s.idx_tup_fetch as tuples_fetched
    FROM pg_indexes i
    LEFT JOIN pg_stat_user_indexes s ON s.indexrelname = i.indexname
    WHERE i.schemaname = %s AND i.tablename = %s
    ORDER BY s.idx_scan DESC NULLS LAST
    """

    current_indexes = pg_query(current_indexes_sql, [schema_name, table_name])

    # 检查未使用的索引
    unused_indexes_sql = """
    SELECT
        schemaname,
        tablename,
        indexname,
        idx_scan,
        pg_size_pretty(pg_relation_size(indexrelid)) as index_size
    FROM pg_stat_user_indexes
    WHERE schemaname = %s AND tablename = %s AND idx_scan = 0
    """

    unused_indexes = pg_query(unused_indexes_sql, [schema_name, table_name])

    return {
        "current_indexes": current_indexes,
        "unused_indexes": unused_indexes,
        "recommendations": [
            "考虑删除未使用的索引以节省空间",
            "对经常用于WHERE条件的列创建索引",
            "对经常用于JOIN的列创建索引",
            "考虑创建复合索引来优化多列查询"
        ]
    }

@mcp.tool()
def smart_query_builder(table_name: str, schema_name: str = "public",
                       columns: Optional[List[str]] = None,
                       conditions: Optional[str] = None,
                       limit: int = 10) -> List[Dict[str, Any]]:
    """
    智能查询构建器，根据参数自动构建和执行查询。
    参数:
        table_name: 表名
        schema_name: 模式名称，默认为public
        columns: 要查询的列名列表，为空则查询所有列
        conditions: WHERE条件，如 "age > 25 AND status = 'active'"
        limit: 限制返回行数，默认10行
    返回:
        查询结果列表
    """
    # 构建列名部分
    if columns:
        columns_str = ", ".join(columns)
    else:
        columns_str = "*"

    # 构建基础查询
    if schema_name and schema_name != "public":
        full_table_name = f"{schema_name}.{table_name}"
    else:
        full_table_name = table_name

    sql = f"SELECT {columns_str} FROM {full_table_name}"

    # 添加WHERE条件
    params = []
    if conditions:
        sql += f" WHERE {conditions}"

    # 添加LIMIT
    sql += f" LIMIT {limit}"

    return pg_query(sql, params)

@mcp.tool()
def get_query_recommendations(table_name: str, schema_name: str = "public") -> Dict[str, Any]:
    """
    基于表结构和统计信息提供查询建议。
    参数:
        table_name: 表名
        schema_name: 模式名称，默认为public
    返回:
        查询建议和优化提示
    """
    # 获取表结构
    columns = get_table_columns(table_name, schema_name)
    indexes = get_table_indexes(table_name, schema_name)

    recommendations = {
        "table_info": {
            "name": table_name,
            "schema": schema_name,
            "column_count": len(columns),
            "index_count": len(indexes)
        },
        "query_suggestions": [],
        "performance_tips": [],
        "sample_queries": []
    }

    # 基于列类型生成查询建议
    for col in columns:
        col_name = col.get('column_name', '')
        data_type = col.get('data_type', '')

        if 'id' in col_name.lower():
            recommendations["query_suggestions"].append(f"使用 {col_name} 进行精确查找")
            recommendations["sample_queries"].append(f"SELECT * FROM {table_name} WHERE {col_name} = ?")

        if 'time' in col_name.lower() or 'date' in col_name.lower():
            recommendations["query_suggestions"].append(f"使用 {col_name} 进行时间范围查询")
            recommendations["sample_queries"].append(f"SELECT * FROM {table_name} WHERE {col_name} >= ? AND {col_name} <= ?")

        if data_type in ['varchar', 'text', 'character varying']:
            recommendations["query_suggestions"].append(f"使用 {col_name} 进行模糊搜索")
            recommendations["sample_queries"].append(f"SELECT * FROM {table_name} WHERE {col_name} ILIKE '%?%'")

    # 性能优化建议
    if indexes:
        recommendations["performance_tips"].append("表已有索引，查询时优先使用索引列作为条件")
        indexed_columns = [idx.get('index_name', '') for idx in indexes]
        recommendations["performance_tips"].append(f"已索引的列: {', '.join(indexed_columns)}")
    else:
        recommendations["performance_tips"].append("表没有索引，考虑为常用查询列创建索引")

    recommendations["performance_tips"].extend([
        "使用LIMIT限制返回行数",
        "避免SELECT *，只查询需要的列",
        "使用参数化查询防止SQL注入",
        "对大表使用分页查询"
    ])

    return recommendations

@mcp.tool()
def explain_query_plan(sql: str, params: Optional[List] = None) -> Dict[str, Any]:
    """
    详细分析查询执行计划，提供性能优化建议。
    参数:
        sql: 要分析的SQL查询语句
        params: SQL参数列表（可选）
    返回:
        详细的执行计划分析和优化建议
    """
    # 获取基本执行计划
    basic_plan = pg_explain(sql, params)

    # 获取详细的执行计划
    detailed_sql = f"EXPLAIN (ANALYZE, BUFFERS, VERBOSE, FORMAT JSON) {sql}"

    conn = get_connection()
    cursor = None
    try:
        conn.set_session(readonly=True)
        cursor = conn.cursor()

        if params:
            cursor.execute(detailed_sql, params)
        else:
            cursor.execute(detailed_sql)

        detailed_plan = cursor.fetchone()[0][0]

        # 分析执行计划并提供建议
        analysis = {
            "basic_plan": basic_plan,
            "detailed_plan": detailed_plan,
            "performance_analysis": analyze_execution_plan(detailed_plan),
            "optimization_suggestions": generate_optimization_suggestions(detailed_plan)
        }

        return analysis

    except Exception as e:
        raise Exception(f"执行计划分析失败: {str(e)}")
    finally:
        if cursor:
            cursor.close()
        conn.close()

def analyze_execution_plan(plan: Dict[str, Any]) -> Dict[str, Any]:
    """分析执行计划的性能特征"""
    analysis = {
        "total_cost": plan.get("Total Cost", 0),
        "execution_time": plan.get("Actual Total Time", 0),
        "rows_returned": plan.get("Actual Rows", 0),
        "node_type": plan.get("Node Type", ""),
        "scan_methods": [],
        "join_methods": [],
        "performance_issues": []
    }

    # 递归分析计划节点
    def analyze_node(node):
        node_type = node.get("Node Type", "")

        if "Scan" in node_type:
            analysis["scan_methods"].append(node_type)

        if "Join" in node_type:
            analysis["join_methods"].append(node_type)

        # 检查性能问题
        if node.get("Actual Total Time", 0) > 1000:  # 超过1秒
            analysis["performance_issues"].append(f"{node_type} 执行时间过长")

        if "Plans" in node:
            for child_plan in node["Plans"]:
                analyze_node(child_plan)

    analyze_node(plan)
    return analysis

def generate_optimization_suggestions(plan: Dict[str, Any]) -> List[str]:
    """基于执行计划生成优化建议"""
    suggestions = []

    def check_node(node):
        node_type = node.get("Node Type", "")

        if node_type == "Seq Scan":
            suggestions.append("发现全表扫描，考虑添加索引")

        if "Hash Join" in node_type and node.get("Actual Total Time", 0) > 100:
            suggestions.append("Hash Join 耗时较长，检查连接条件和数据分布")

        if "Sort" in node_type:
            suggestions.append("发现排序操作，考虑创建相应的索引")

        if "Plans" in node:
            for child_plan in node["Plans"]:
                check_node(child_plan)

    check_node(plan)

    if not suggestions:
        suggestions.append("查询执行计划看起来不错，没有明显的性能问题")

    return suggestions

if __name__ == "__main__":
    mcp.run(transport="sse")  # 使用SSE传输
