from mcp.server.fastmcp import FastMCP

# 创建MCP服务器
mcp = FastMCP("HelloWorld")

@mcp.tool()
def helloworld() -> str:
    """hello world"""
    return "hello world for mcp"

@mcp.tool()
def get_rank_by_name(name: str) -> str:
    """根据员工的姓名获取该员工的期末评级"""
    if name == "张三":
        return "name: 张三 期末评级: S+"
    elif name == "李四":
        return "name: 李四 期末评级: A-"
    else:
        return "未搜到该员工的期末评级"

if __name__ == "__main__":
    # 使用SSE传输
    mcp.run(transport="sse")
    
