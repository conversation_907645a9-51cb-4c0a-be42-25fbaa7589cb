from mcp.server.fastmcp import FastMCP
import pymysql

mcp = FastMCP("MySQLMCP", port=9009, host="0.0.0.0")

@mcp.tool()
def read_data(age: int) -> int:
    """
    统计user表中年龄大于X的记录数。
    参数:
        age: 年龄阈值，只统计大于该值的用户数量。
    返回:
        满足条件的记录数。
    """
    conn = pymysql.connect(
        host="localhost",
        port=3306,
        user="root",
        password="123456",
        database="mcp"
    )
    cursor = conn.cursor()
    cursor.execute(f"SELECT COUNT(*) FROM user where age > {age}")
    result = cursor.fetchone()[0]
    cursor.close()
    conn.close()
    return result

@mcp.tool()
def add_user(name: str, age: int) -> int:
    """
    向user表中添加一条新用户记录。
    参数:
        name: 用户名。
        age: 年龄。
    返回:
        新增用户的id。
    """
    conn = pymysql.connect(
        host="localhost",
        port=3306,
        user="root",
        password="123456",
        database="mcp"
    )
    cursor = conn.cursor()
    cursor.execute("INSERT INTO user (name, age) VALUES (%s, %s)", (name, age))
    conn.commit()
    new_id = cursor.lastrowid
    cursor.close()
    conn.close()
    return new_id

@mcp.tool()
def delete_user(user_id: int) -> int:
    """
    根据用户id删除user表中的一条记录。
    参数:
        user_id: 要删除的用户id。
    返回:
        实际删除的记录数（0或1）。
    """
    conn = pymysql.connect(
        host="localhost",
        port=3306,
        user="root",
        password="123456",
        database="mcp"
    )
    cursor = conn.cursor()
    cursor.execute("DELETE FROM user WHERE id = %s", (user_id,))
    conn.commit()
    affected = cursor.rowcount
    cursor.close()
    conn.close()
    return affected

@mcp.tool()
def update_user(user_id: int, name: str = None, age: int = None) -> int:
    """
    根据用户id更新user表中的用户名和/或年龄。
    参数:
        user_id: 要更新的用户id。
        name: 新用户名（可选）。
        age: 新年龄（可选）。
    返回:
        实际更新的记录数（0或1）。
    """
    conn = pymysql.connect(
        host="localhost",
        port=3306,
        user="root",
        password="123456",
        database="mcp"
    )
    cursor = conn.cursor()
    fields = []
    values = []
    if name is not None:
        fields.append("name = %s")
        values.append(name)
    if age is not None:
        fields.append("age = %s")
        values.append(age)
    if not fields:
        cursor.close()
        conn.close()
        return 0  # 没有要更新的内容
    values.append(user_id)
    sql = f"UPDATE user SET {', '.join(fields)} WHERE id = %s"
    cursor.execute(sql, tuple(values))
    conn.commit()
    affected = cursor.rowcount
    cursor.close()
    conn.close()
    return affected

@mcp.tool()
def delete_duplicate_names() -> int:
    """
    删除user表中name重复的多余行，只保留每个name的最小id那一行。
    返回:
        被删除的记录数。
    """
    conn = pymysql.connect(
        host="localhost",
        port=3306,
        user="root",
        password="123456",
        database="mcp"
    )
    cursor = conn.cursor()
    sql = """
        DELETE FROM user
        WHERE id NOT IN (
            SELECT min_id FROM (
                SELECT MIN(id) as min_id FROM user GROUP BY name
            ) as t
        )
    """
    affected = cursor.execute(sql)
    conn.commit()
    cursor.close()
    conn.close()
    return affected

@mcp.tool()
def get_users(min_age: int = None, max_age: int = None) -> list:
    """
    查询user表中满足条件的用户数据，可按年龄范围筛选。
    参数:
        min_age: 最小年龄（可选），只返回年龄大于等于该值的用户。
        max_age: 最大年龄（可选），只返回年龄小于等于该值的用户。
    返回:
        用户信息的字典列表，每个字典包含id、name和age。
    """
    conn = pymysql.connect(
        host="localhost",
        port=3306,
        user="root",
        password="123456",
        database="mcp"
    )
    cursor = conn.cursor()
    sql = "SELECT id, name, age FROM user"
    conditions = []
    params = []
    if min_age is not None:
        conditions.append("age >= %s")
        params.append(min_age)
    if max_age is not None:
        conditions.append("age <= %s")
        params.append(max_age)
    if conditions:
        sql += " WHERE " + " AND ".join(conditions)
    cursor.execute(sql, tuple(params))
    result = cursor.fetchall()
    cursor.close()
    conn.close()
    return [{"id": row[0], "name": row[1], "age": row[2]} for row in result]

if __name__ == "__main__":
    mcp.run(transport="sse")  # 使用SSE传输
