from mcp.server.fastmcp import FastMCP
import pymysql
from contextlib import contextmanager
from typing import Optional, List, Dict, Any
import logging
from pymysql.err import Error as PyMySQLError

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatabasePool:
    """简单的数据库连接池"""
    def __init__(self, host: str, port: int, user: str, password: str, database: str, max_connections: int = 5):
        self.config = {
            'host': host,
            'port': port,
            'user': user,
            'password': password,
            'database': database,
            'charset': 'utf8mb4',
            'autocommit': True
        }
        self.max_connections = max_connections
        self._pool = []
        self._used_connections = set()
    
    def get_connection(self):
        """获取数据库连接"""
        # 尝试从池中获取可用连接
        while self._pool:
            conn = self._pool.pop()
            try:
                conn.ping(reconnect=True)  # 检查连接是否有效
                self._used_connections.add(conn)
                return conn
            except:
                conn.close()
        
        # 如果池为空或连接无效，创建新连接
        if len(self._used_connections) < self.max_connections:
            conn = pymysql.connect(**self.config)
            self._used_connections.add(conn)
            return conn
        
        raise Exception("连接池已满，无法创建新连接")
    
    def return_connection(self, conn):
        """归还连接到池中"""
        if conn in self._used_connections:
            self._used_connections.remove(conn)
            try:
                if conn.open:
                    self._pool.append(conn)
            except:
                conn.close()
    
    def close_all(self):
        """关闭所有连接"""
        for conn in list(self._used_connections):
            conn.close()
        for conn in self._pool:
            conn.close()
        self._used_connections.clear()
        self._pool.clear()

# 初始化连接池
db_pool = DatabasePool(
    host="localhost",
    port=3306,
    user="root",
    password="123456",
    database="mcp"
)

@contextmanager
def get_db_connection():
    """数据库连接上下文管理器"""
    conn = None
    try:
        conn = db_pool.get_connection()
        yield conn
    except PyMySQLError as e:
        logger.error(f"数据库错误: {e}")
        if conn:
            conn.rollback()
        raise
    except Exception as e:
        logger.error(f"未知错误: {e}")
        if conn:
            conn.rollback()
        raise
    finally:
        if conn:
            db_pool.return_connection(conn)

mcp = FastMCP("MySQLMCP", port=9009, host="0.0.0.0")

@mcp.tool()
def read_data(age: int) -> int:
    """
    统计user表中年龄大于X的记录数。
    参数:
        age: 年龄阈值，只统计大于该值的用户数量。
    返回:
        满足条件的记录数。
    """
    with get_db_connection() as conn:
        with conn.cursor() as cursor:
            cursor.execute("SELECT COUNT(*) FROM user WHERE age > %s", (age,))
            return cursor.fetchone()[0]

@mcp.tool()
def add_user(name: str, age: int) -> Dict[str, Any]:
    """
    向user表中添加一条新用户记录。
    参数:
        name: 用户名。
        age: 年龄。
    返回:
        包含新增用户信息的字典。
    """
    if not name.strip():
        raise ValueError("用户名不能为空")
    if age < 0 or age > 150:
        raise ValueError("年龄必须在0-150之间")
    
    with get_db_connection() as conn:
        with conn.cursor() as cursor:
            cursor.execute("INSERT INTO user (name, age) VALUES (%s, %s)", (name.strip(), age))
            new_id = cursor.lastrowid
            return {"id": new_id, "name": name.strip(), "age": age, "message": "用户添加成功"}

@mcp.tool()
def delete_user(user_id: int) -> Dict[str, Any]:
    """
    根据用户id删除user表中的一条记录。
    参数:
        user_id: 要删除的用户id。
    返回:
        删除操作的结果信息。
    """
    with get_db_connection() as conn:
        with conn.cursor() as cursor:
            cursor.execute("DELETE FROM user WHERE id = %s", (user_id,))
            affected = cursor.rowcount
            return {
                "deleted_count": affected,
                "message": f"成功删除 {affected} 条记录" if affected > 0 else "未找到要删除的用户"
            }

@mcp.tool()
def update_user(user_id: int, name: Optional[str] = None, age: Optional[int] = None) -> Dict[str, Any]:
    """
    根据用户id更新user表中的用户名和/或年龄。
    参数:
        user_id: 要更新的用户id。
        name: 新用户名（可选）。
        age: 新年龄（可选）。
    返回:
        更新操作的结果信息。
    """
    if name is not None and not name.strip():
        raise ValueError("用户名不能为空")
    if age is not None and (age < 0 or age > 150):
        raise ValueError("年龄必须在0-150之间")
    
    with get_db_connection() as conn:
        with conn.cursor() as cursor:
            fields = []
            values = []
            
            if name is not None:
                fields.append("name = %s")
                values.append(name.strip())
            if age is not None:
                fields.append("age = %s")
                values.append(age)
            
            if not fields:
                return {"updated_count": 0, "message": "没有要更新的内容"}
            
            values.append(user_id)
            sql = f"UPDATE user SET {', '.join(fields)} WHERE id = %s"
            cursor.execute(sql, tuple(values))
            affected = cursor.rowcount
            
            return {
                "updated_count": affected,
                "message": f"成功更新 {affected} 条记录" if affected > 0 else "未找到要更新的用户"
            }

@mcp.tool()
def delete_duplicate_names() -> Dict[str, Any]:
    """
    删除user表中name重复的多余行，只保留每个name的最小id那一行。
    返回:
        删除操作的结果信息。
    """
    with get_db_connection() as conn:
        with conn.cursor() as cursor:
            # 先查询重复的记录数
            cursor.execute("""
                SELECT COUNT(*) - COUNT(DISTINCT name) as duplicate_count 
                FROM user
            """)
            duplicate_count = cursor.fetchone()[0]
            
            if duplicate_count == 0:
                return {"deleted_count": 0, "message": "没有发现重复的用户名"}
            
            # 删除重复记录
            sql = """
                DELETE FROM user
                WHERE id NOT IN (
                    SELECT min_id FROM (
                        SELECT MIN(id) as min_id FROM user GROUP BY name
                    ) as t
                )
            """
            cursor.execute(sql)
            affected = cursor.rowcount
            
            return {
                "deleted_count": affected,
                "message": f"成功删除 {affected} 条重复记录"
            }

@mcp.tool()
def get_users(min_age: Optional[int] = None, max_age: Optional[int] = None, limit: int = 100, offset: int = 0) -> Dict[str, Any]:
    """
    查询user表中满足条件的用户数据，支持分页和年龄范围筛选。
    参数:
        min_age: 最小年龄（可选），只返回年龄大于等于该值的用户。
        max_age: 最大年龄（可选），只返回年龄小于等于该值的用户。
        limit: 返回记录数限制，默认100。
        offset: 偏移量，用于分页，默认0。
    返回:
        包含用户列表和分页信息的字典。
    """
    if limit <= 0 or limit > 1000:
        raise ValueError("limit必须在1-1000之间")
    if offset < 0:
        raise ValueError("offset不能为负数")
    
    with get_db_connection() as conn:
        with conn.cursor() as cursor:
            # 构建查询条件
            conditions = []
            params = []
            
            if min_age is not None:
                conditions.append("age >= %s")
                params.append(min_age)
            if max_age is not None:
                conditions.append("age <= %s")
                params.append(max_age)
            
            where_clause = " WHERE " + " AND ".join(conditions) if conditions else ""
            
            # 查询总数
            count_sql = f"SELECT COUNT(*) FROM user{where_clause}"
            cursor.execute(count_sql, tuple(params))
            total_count = cursor.fetchone()[0]
            
            # 查询数据
            data_sql = f"SELECT id, name, age FROM user{where_clause} ORDER BY id LIMIT %s OFFSET %s"
            cursor.execute(data_sql, tuple(params + [limit, offset]))
            result = cursor.fetchall()
            
            users = [{"id": row[0], "name": row[1], "age": row[2]} for row in result]
            
            return {
                "users": users,
                "total_count": total_count,
                "current_page": offset // limit + 1,
                "page_size": limit,
                "has_next": offset + limit < total_count
            }

@mcp.tool()
def get_user_stats() -> Dict[str, Any]:
    """
    获取用户表的统计信息。
    返回:
        包含各种统计数据的字典。
    """
    with get_db_connection() as conn:
        with conn.cursor() as cursor:
            stats = {}
            
            # 总用户数
            cursor.execute("SELECT COUNT(*) FROM user")
            stats["total_users"] = cursor.fetchone()[0]
            
            # 年龄统计
            cursor.execute("SELECT MIN(age), MAX(age), AVG(age) FROM user WHERE age IS NOT NULL")
            age_stats = cursor.fetchone()
            stats["age_stats"] = {
                "min_age": age_stats[0],
                "max_age": age_stats[1],
                "avg_age": round(age_stats[2], 2) if age_stats[2] else None
            }
            
            # 重复姓名统计
            cursor.execute("""
                SELECT COUNT(*) - COUNT(DISTINCT name) as duplicate_names
                FROM user
            """)
            stats["duplicate_names"] = cursor.fetchone()[0]
            
            return stats

# 优雅关闭处理
import atexit
atexit.register(db_pool.close_all)

if __name__ == "__main__":
    try:
        logger.info("启动 MySQL MCP Server...")
        mcp.run(transport="sse")
    except KeyboardInterrupt:
        logger.info("正在关闭服务器...")
    finally:
        db_pool.close_all()
        logger.info("服务器已关闭")