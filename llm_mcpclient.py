# MCP (Model Context Protocol) 客户端实现
# 该客户端连接到MCP服务器，获取可用工具，并通过Ollama LLM进行function calling

import asyncio  # 异步编程支持
import json     # JSON数据处理
import sys      # 系统相关功能，用于获取命令行参数
from typing import Optional  # 类型注解，表示可选类型
from contextlib import AsyncExitStack  # 异步上下文管理器，用于资源清理
from openai import OpenAI  # OpenAI客户端，兼容Ollama API
from mcp import ClientSession  # MCP客户端会话
from mcp.client.sse import sse_client  # MCP SSE(Server-Sent Events)客户端


class MCPClient:
    def __init__(self):
        """
        初始化 MCP 客户端
        
        设置连接参数：
        - Ollama API 配置（兼容 OpenAI API 格式）
        - MCP 会话管理
        - 资源清理管理器
        """
        # 异步资源管理器，用于自动清理连接等资源
        self.exit_stack = AsyncExitStack()
        
        # Ollama 服务器配置 - 使用兼容 OpenAI API 的格式
        self.openai_api_key = "ollama"  # Ollama 不需要真实 API key，使用占位符
        self.base_url = "http://192.168.21.14:11434/v1"  # Ollama 服务器地址，/v1 是 OpenAI 兼容接口
        self.model = "qwen2.5:7b"  # 要使用的模型名称

        # 初始化 OpenAI 客户端（实际连接到 Ollama）
        self.client = OpenAI(api_key=self.openai_api_key, base_url=self.base_url)
        
        # MCP 会话对象，初始化为 None，连接后才会设置
        self.session: Optional[ClientSession] = None

    async def connect_to_server(self, sse_url: str):
        """
        连接到 MCP SSE 服务器并列出可用工具
        
        Args:
            sse_url: MCP 服务器的 SSE (Server-Sent Events) 端点 URL
                    例如: "http://localhost:9009/sse"
        
        功能说明:
        1. 建立 SSE 连接到 MCP 服务器
        2. 创建 MCP 客户端会话
        3. 初始化会话（握手过程）
        4. 获取并显示服务器提供的所有工具
        """
        # 建立 SSE 传输连接
        # SSE 是一种服务器向客户端推送数据的技术，MCP 使用它进行双向通信
        sse_transport = await self.exit_stack.enter_async_context(
            sse_client(sse_url)
        )
        
        # 获取读写流，用于与服务器通信
        self.read, self.write = sse_transport
        
        # 创建 MCP 客户端会话，管理与服务器的所有交互
        self.session = await self.exit_stack.enter_async_context(
            ClientSession(self.read, self.write)
        )
        
        # 执行 MCP 协议的初始化握手
        await self.session.initialize()

        # 从 MCP 服务器获取可用工具列表
        response = await self.session.list_tools()
        tools = response.tools
        
        # 显示连接成功信息和可用工具
        print(f"\n已连接到 SSE 服务器 ({sse_url})，支持以下工具:")
        for tool in tools:
            # 安全地处理可能为 None 的描述
            description = tool.description.strip() if tool.description else "无描述"
            print(f"  - {tool.name}: {description}")

    async def process_query(self, query: str) -> str:
        """
        使用 Ollama 处理查询并调用可用的 MCP 工具 (Function Calling)
        
        Args:
            query: 用户输入的查询文本
            
        Returns:
            str: 处理后的响应文本
            
        工作流程:
        1. 构建对话消息
        2. 获取 MCP 工具列表并转换为 OpenAI Function Calling 格式
        3. 调用 Ollama API 进行推理
        4. 如果需要使用工具，则调用 MCP 工具并获取结果
        5. 将工具结果返回给 Ollama 生成最终响应
        """
        # 初始化对话消息列表，包含用户查询
        messages = [{"role": "user", "content": query}]
        
        # 检查 session 是否已初始化
        if not self.session:
            return "错误: MCP 会话未初始化，请先连接到服务器"
        
        # 获取 MCP 服务器提供的工具列表
        response = await self.session.list_tools()
        
        # 将 MCP 工具格式转换为 OpenAI Function Calling 格式
        # 这样 Ollama 就能理解并调用这些工具
        available_tools = [{
            "type": "function",  # OpenAI 标准格式要求
            "function": {
                "name": tool.name,  # 工具名称
                "description": tool.description,  # 工具描述
                "parameters": tool.inputSchema  # 工具参数 schema（JSON Schema 格式）
            }
        } for tool in response.tools]

        try:
            # 向 Ollama 发送请求，包含可用工具信息
            response = self.client.chat.completions.create(
                model=self.model,  # 使用配置的模型
                messages=messages,  # 对话历史
                tools=available_tools  # 可用工具列表
            )

            # 处理 Ollama 的响应
            choice = response.choices[0]
            
            # 检查是否需要调用工具
            if choice.finish_reason == "tool_calls" and choice.message.tool_calls:
                # Ollama 决定需要使用工具
                tool_call = choice.message.tool_calls[0]  # 获取第一个工具调用
                tool_name = tool_call.function.name  # 工具名称
                tool_args = json.loads(tool_call.function.arguments)  # 解析工具参数

                # 打印调试信息
                print(f"\n🔧 调用工具: {tool_name}")
                print(f"📝 参数: {tool_args}")

                # 通过 MCP 执行工具调用
                result = await self.session.call_tool(tool_name, tool_args)
                
                # 将 LLM 的消息（包含工具调用）添加到历史中
                messages.append(choice.message.model_dump())
                
                # 提取工具执行结果的文本内容
                # 处理可能的不同内容类型
                tool_result_content = ""
                if result.content and len(result.content) > 0:
                    first_content = result.content[0]
                    if hasattr(first_content, 'text'):
                        tool_result_content = first_content.text
                    else:
                        tool_result_content = str(first_content)
                
                # 将工具执行结果添加到对话历史
                messages.append({
                    "role": "tool",  # 表示这是工具的响应
                    "content": tool_result_content,  # 工具返回的内容
                    "tool_call_id": tool_call.id,  # 关联到对应的工具调用
                })

                # 将包含工具结果的完整对话发送给 Ollama，生成最终响应
                final_response = self.client.chat.completions.create(
                    model=self.model,
                    messages=messages,  # 包含工具调用和结果的完整对话
                )
                
                # 返回最终响应，安全处理可能的 None 值
                return final_response.choices[0].message.content or "无响应内容"

            # 如果不需要工具调用，直接返回 LLM 的响应
            return choice.message.content or "无响应内容"
            
        except Exception as e:
            # 捕获并返回错误信息
            return f"调用 Ollama 时出错: {str(e)}"

    async def chat_loop(self):
        """
        运行交互式聊天循环
        
        功能说明:
        1. 提供用户友好的命令行界面
        2. 持续接收用户输入
        3. 调用 process_query 处理每个查询
        4. 显示 Ollama 的响应
        5. 处理用户退出和异常情况
        
        用户可以输入 'quit' 退出程序
        支持 Ctrl+C 快捷键退出
        """
        print(f"\n🤖 MCP 客户端已启动！使用模型: {self.model}")
        print("输入 'quit' 退出")
        
        while True:
            try:
                # 获取用户输入，去除首尾空格
                query = input("\n你: ").strip()
                
                # 检查退出命令
                if query.lower() == 'quit':
                    break
                    
                # 如果输入为空，跳过处理
                if not query:
                    continue
                    
                print("\n🤖 思考中...")
                
                # 处理用户查询
                response = await self.process_query(query)
                
                # 显示 Ollama 的响应
                print(f"\n🤖 Ollama: {response}")
                
            except KeyboardInterrupt:
                # 用户按 Ctrl+C 退出
                print("\n\n👋 再见！")
                break
            except Exception as e:
                # 处理其他异常，但不退出程序
                print(f"\n⚠️ 发生错误: {str(e)}")

    async def cleanup(self):
        """
        清理资源
        
        功能说明:
        - 关闭所有异步资源（MCP 连接、SSE 连接等）
        - 确保程序正确退出，避免资源泄漏
        - 由 AsyncExitStack 自动管理所有已注册的资源
        """
        await self.exit_stack.aclose()


async def main():
    """
    主函数 - 程序入口点
    
    功能说明:
    1. 检查命令行参数（需要提供 MCP 服务器的 SSE URL）
    2. 创建 MCP 客户端实例
    3. 连接到 MCP 服务器
    4. 启动交互式聊天循环
    5. 确保程序退出时正确清理资源
    
    命令行参数:
        sse_url: MCP 服务器的 SSE 端点 URL
                例如: http://localhost:9009/sse
    
    使用示例:
        python llm_mcpclient.py http://localhost:9009/sse
    """
    # 检查命令行参数
    if len(sys.argv) < 2:
        print("Usage: python client.py <sse_url>")
        print("Example: python client.py http://localhost:9009/sse")
        print("\n说明:")
        print("  sse_url: MCP 服务器的 SSE (Server-Sent Events) 端点地址")
        print("  该地址通常由 MCP 服务器提供，用于建立实时通信连接")
        sys.exit(1)

    # 创建 MCP 客户端实例
    client = MCPClient()
    
    try:
        # 连接到指定的 MCP 服务器
        await client.connect_to_server(sys.argv[1])
        
        # 启动交互式聊天循环
        await client.chat_loop()
        
    finally:
        # 无论程序如何退出，都要清理资源
        # 这确保了 SSE 连接、MCP 会话等资源被正确释放
        await client.cleanup()


if __name__ == "__main__":
    """
    程序入口点
    
    当脚本直接运行时（而不是作为模块导入），执行 main() 函数
    使用 asyncio.run() 来运行异步主函数
    """
    asyncio.run(main())