import asyncio
import json
import sys
from typing import Optional
from contextlib import AsyncExitStack
from openai import OpenAI
from mcp import ClientSession
from mcp.client.sse import sse_client


class MCPClient:
    def __init__(self):
        """初始化 MCP 客户端"""
        self.exit_stack = AsyncExitStack()
        
        # Ollama 配置
        self.openai_api_key = "ollama"
        self.base_url = "http://192.168.21.14:11434/v1"
        self.model = "qwen2.5:7b"

        # 初始化 OpenAI 客户端（兼容 Ollama）
        self.client = OpenAI(api_key=self.openai_api_key, base_url=self.base_url)
        self.session: Optional[ClientSession] = None

    async def connect_to_server(self, sse_url: str):
        """连接到 MCP SSE 服务器并列出可用工具"""
        # 建立 SSE 连接
        sse_transport = await self.exit_stack.enter_async_context(
            sse_client(sse_url)
        )
        self.read, self.write = sse_transport
        self.session = await self.exit_stack.enter_async_context(
            ClientSession(self.read, self.write)
        )
        await self.session.initialize()

        # 列出 MCP 服务器上的工具
        response = await self.session.list_tools()
        tools = response.tools
        print(f"\n已连接到 SSE 服务器 ({sse_url})，支持以下工具:")
        for tool in tools:
            print(f"  - {tool.name}: {tool.description.strip()}")

    async def process_query(self, query: str) -> str:
        """
        使用 Ollama 处理查询并调用可用的 MCP 工具 (Function Calling)
        """
        messages = [{"role": "user", "content": query}]
        
        # 获取可用工具列表
        response = await self.session.list_tools()
        available_tools = [{
            "type": "function",
            "function": {
                "name": tool.name,
                "description": tool.description,
                "parameters": tool.inputSchema
            }
        } for tool in response.tools]

        try:
            # 调用 Ollama API
            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                tools=available_tools
            )

            # 处理返回的内容
            choice = response.choices[0]
            if choice.finish_reason == "tool_calls" and choice.message.tool_calls:
                # 如果需要使用工具，解析工具调用
                tool_call = choice.message.tool_calls[0]
                tool_name = tool_call.function.name
                tool_args = json.loads(tool_call.function.arguments)

                print(f"\n🔧 调用工具: {tool_name}")
                print(f"📝 参数: {tool_args}")

                # 执行工具
                result = await self.session.call_tool(tool_name, tool_args)
                
                # 将结果存入消息历史
                messages.append(choice.message.model_dump())
                messages.append({
                    "role": "tool",
                    "content": result.content[0].text,
                    "tool_call_id": tool_call.id,
                })

                # 将结果返回给 Ollama 生成最终响应
                final_response = self.client.chat.completions.create(
                    model=self.model,
                    messages=messages,
                )
                return final_response.choices[0].message.content

            return choice.message.content
            
        except Exception as e:
            return f"调用 Ollama 时出错: {str(e)}"

    async def chat_loop(self):
        """运行交互式聊天循环"""
        print(f"\n🤖 MCP 客户端已启动！使用模型: {self.model}")
        print("输入 'quit' 退出")
        
        while True:
            try:
                query = input("\n你: ").strip()
                if query.lower() == 'quit':
                    break
                    
                print("\n🤖 思考中...")
                response = await self.process_query(query)
                print(f"\n🤖 Ollama: {response}")
                
            except KeyboardInterrupt:
                print("\n\n👋 再见！")
                break
            except Exception as e:
                print(f"\n⚠️ 发生错误: {str(e)}")

    async def cleanup(self):
        """清理资源"""
        await self.exit_stack.aclose()


async def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("Usage: python client.py <sse_url>")
        print("Example: python client.py http://localhost:9009/sse")
        sys.exit(1)

    client = MCPClient()
    try:
        await client.connect_to_server(sys.argv[1])
        await client.chat_loop()
    finally:
        await client.cleanup()


if __name__ == "__main__":
    asyncio.run(main())