# MCP (Model Context Protocol) 技术分享

## 1. 什么是MCP？

MCP (Model Context Protocol) 是一个开放标准协议，用于连接AI模型与外部数据源和工具。它允许AI模型安全地访问和操作外部系统，如数据库、API、文件系统等。

### 核心概念
- **服务端 (Server)**: 提供工具和资源的服务
- **客户端 (Client)**: 使用工具和资源的AI应用
- **工具 (Tools)**: 服务端暴露给客户端的功能接口
- **传输层**: 支持多种传输方式（SSE、WebSocket等）

## 2. 项目架构概览

```
MCP Demo 项目
├── mysql_mcp.py      # MCP服务端 - 提供MySQL数据库操作工具
└── llm_mcpclient.py  # MCP客户端 - 连接Ollama模型和MCP服务
```

## 3. MCP服务端实现 (mysql_mcp.py)

### 3.1 技术栈
- **FastMCP**: 快速构建MCP服务的框架
- **PyMySQL**: Python MySQL数据库连接器
- **SSE传输**: Server-Sent Events 实时通信

### 3.2 服务配置
```python
from mcp.server.fastmcp import FastMCP
mcp = FastMCP("MySQLMCP", port=9009, host="0.0.0.0")
```

### 3.3 提供的数据库工具

#### 📊 数据查询工具
- **read_data(age)**: 统计年龄大于指定值的用户数量
- **get_users(min_age, max_age)**: 按年龄范围查询用户列表

#### ✏️ 数据操作工具
- **add_user(name, age)**: 添加新用户
- **update_user(user_id, name, age)**: 更新用户信息
- **delete_user(user_id)**: 删除指定用户

#### 🧹 数据清理工具
- **delete_duplicate_names()**: 删除重复姓名的用户记录

### 3.4 工具装饰器模式
```python
@mcp.tool()
def read_data(age: int) -> int:
    """
    统计user表中年龄大于X的记录数。
    参数:
        age: 年龄阈值，只统计大于该值的用户数量。
    返回:
        满足条件的记录数。
    """
    # 数据库操作逻辑
```

## 4. MCP客户端实现 (llm_mcpclient.py)

### 4.1 技术栈
- **OpenAI SDK**: 兼容Ollama的API调用
- **MCP Client**: 官方MCP客户端库
- **SSE Client**: Server-Sent Events客户端
- **Ollama**: 本地大语言模型服务

### 4.2 核心功能

#### 🔗 连接管理
```python
async def connect_to_server(self, sse_url: str):
    # 建立SSE连接
    sse_transport = await self.exit_stack.enter_async_context(
        sse_client(sse_url)
    )
    # 初始化会话
    self.session = await self.exit_stack.enter_async_context(
        ClientSession(self.read, self.write)
    )
```

#### 🤖 Function Calling 实现
1. **获取可用工具**: 从MCP服务端获取工具列表
2. **LLM推理**: 将工具信息传递给Ollama模型
3. **工具调用**: 根据模型决策调用相应工具
4. **结果整合**: 将工具执行结果返回给模型生成最终回答

## 5. 工作流程演示

### 5.1 启动服务端
```bash
python mysql_mcp.py
# 服务运行在 http://localhost:9009/sse
```

### 5.2 启动客户端
```bash
python llm_mcpclient.py http://localhost:9009/sse
```

### 5.3 交互示例
```
你: 帮我查询年龄大于25岁的用户有多少个？

🔧 调用工具: read_data
📝 参数: {"age": 25}

🤖 Ollama: 根据查询结果，年龄大于25岁的用户共有12个。
```

## 6. 技术亮点

### 6.1 类型安全
- 使用Python类型注解确保参数类型正确
- 自动生成工具的JSON Schema

### 6.2 异步处理
- 全异步架构，支持高并发
- 使用AsyncExitStack管理资源生命周期

### 6.3 错误处理
- 完善的异常捕获和错误信息返回
- 资源自动清理机制

### 6.4 灵活的传输层
- 支持SSE实时通信
- 可扩展支持WebSocket等其他传输方式

## 7. 应用场景

### 7.1 数据库智能查询
- 自然语言转SQL查询
- 复杂数据分析和报表生成

### 7.2 系统集成
- 连接多个外部系统
- 统一的AI接口层

### 7.3 工具链扩展
- 文件操作、API调用
- 自定义业务逻辑工具

## 8. 优势与特点

### ✅ 优势
- **标准化**: 遵循开放协议，互操作性强
- **安全性**: 工具调用权限可控
- **扩展性**: 易于添加新工具和功能
- **性能**: 异步架构，响应迅速

### 🎯 适用场景
- 企业内部AI助手
- 数据分析和BI工具
- 自动化运维系统
- 智能客服系统

## 9. 总结

MCP协议为AI模型与外部系统的集成提供了标准化解决方案。通过这个Demo，我们展示了：

1. **服务端**: 如何快速构建MCP工具服务
2. **客户端**: 如何集成本地LLM和MCP服务
3. **Function Calling**: 实现智能工具调用
4. **实际应用**: 数据库操作的自然语言接口

这种架构模式可以轻松扩展到更多业务场景，为企业AI应用提供强大的基础设施支持。

---

## Q&A 环节

欢迎大家提问！ 🙋‍♂️🙋‍♀️
