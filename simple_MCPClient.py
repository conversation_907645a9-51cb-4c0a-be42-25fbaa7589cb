from mcp import ClientSession
from mcp.client.sse import sse_client

async def run():
    # SSE服务器的URL
    sse_url = "http://localhost:9010/sse"
    
    # 创建SSE客户端连接
    async with sse_client(sse_url) as (read, write):
        async with ClientSession(read, write) as session:
            # 初始化连接
            await session.initialize()

            # 列出可用工具
            tools = await session.list_tools()
            print("Tools:", tools)

if __name__ == "__main__":
    import asyncio
    asyncio.run(run())